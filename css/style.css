/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 主容器 */
.dashboard-container {
    width: 100%;
    min-height: 100vh;
    padding: 20px;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

/* 头部样式 */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.main-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00d4ff, #ff00ff, #00ff88);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 15px;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

.title-icon {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

.subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 300;
    margin-left: 10px;
}

.header-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.current-time {
    font-size: 1.2rem;
    font-weight: 600;
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #00ff88;
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* 主要内容区域 */
.dashboard-main {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* 指标卡片行 */
.metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.metric-card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00d4ff, #ff00ff, #00ff88);
    animation: shimmer 2s linear infinite;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    background: rgba(255, 255, 255, 0.12);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.metric-header h3 {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.metric-icon {
    font-size: 1.5rem;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.metric-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.metric-change {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.metric-change.positive {
    color: #00ff88;
    background: rgba(0, 255, 136, 0.1);
}

.metric-change.negative {
    color: #ff4757;
    background: rgba(255, 71, 87, 0.1);
}

/* 图表行样式 */
.charts-row-main,
.charts-row-secondary,
.charts-row-monitoring,
.charts-row-finance {
    display: grid;
    gap: 20px;
    align-items: stretch;
}

.charts-row-main {
    grid-template-columns: 2fr 1fr;
}

.charts-row-secondary {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.charts-row-monitoring {
    grid-template-columns: 1fr 1fr 0.8fr;
}

.charts-row-finance {
    grid-template-columns: 2fr 1fr 1fr;
}

/* 图表容器 */
.chart-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.5), transparent);
    animation: scan 3s linear infinite;
}

.chart-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header h3 {
    font-size: 1.1rem;
    color: #ffffff;
    font-weight: 600;
}

.live-indicator {
    font-size: 0.8rem;
    color: #ff4757;
    animation: blink 1s infinite;
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-small:hover,
.btn-small.active {
    background: rgba(0, 212, 255, 0.3);
    border-color: #00d4ff;
    color: #ffffff;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.chart-content {
    height: 300px;
    width: 100%;
}

.chart-container.small .chart-content {
    height: 250px;
}

.chart-container.medium .chart-content {
    height: 320px;
}

.chart-container.large .chart-content {
    height: 350px;
}

/* 底部样式 */
.dashboard-footer {
    margin-top: 30px;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* 动画效果 */
@keyframes titleGlow {
    0% { filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.5)); }
    100% { filter: drop-shadow(0 0 20px rgba(255, 0, 255, 0.8)); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.2); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .charts-row-main {
        grid-template-columns: 1fr;
    }
    
    .charts-row-monitoring {
        grid-template-columns: 1fr 1fr;
    }
    
    .charts-row-finance {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 15px;
    }
    
    .main-title {
        font-size: 1.8rem;
    }
    
    .metrics-row {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .charts-row-secondary,
    .charts-row-monitoring,
    .charts-row-finance {
        grid-template-columns: 1fr;
    }
    
    .header-info {
        align-items: flex-start;
    }
    
    .footer-info {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
