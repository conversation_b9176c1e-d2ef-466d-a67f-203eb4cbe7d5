// BI大屏数据可视化主控制器
class DashboardController {
    constructor() {
        this.charts = {};
        this.updateInterval = null;
        this.init();
    }

    // 初始化仪表板
    init() {
        this.updateTime();
        this.initializeCharts();
        this.updateMetrics();
        this.startAutoUpdate();
        
        // 设置时间更新
        setInterval(() => this.updateTime(), 1000);
    }

    // 更新时间显示
    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        document.getElementById('currentTime').textContent = timeString;
        document.getElementById('lastUpdateTime').textContent = timeString;
    }

    // 生成模拟数据
    generateMockData() {
        return {
            totalSales: Math.floor(Math.random() * 10000000) + 5000000,
            activeUsers: Math.floor(Math.random() * 50000) + 20000,
            totalOrders: Math.floor(Math.random() * 10000) + 5000,
            conversionRate: (Math.random() * 10 + 15).toFixed(1),
            onlineUsers: Math.floor(Math.random() * 2000) + 1000
        };
    }

    // 更新核心指标
    updateMetrics() {
        const data = this.generateMockData();
        
        document.getElementById('totalSales').textContent = 
            `¥${(data.totalSales / 10000).toFixed(1)}万`;
        document.getElementById('activeUsers').textContent = 
            `${(data.activeUsers / 10000).toFixed(1)}万`;
        document.getElementById('totalOrders').textContent = 
            `${data.totalOrders.toLocaleString()}`;
        document.getElementById('conversionRate').textContent = 
            `${data.conversionRate}%`;
        document.getElementById('onlineUsers').textContent = 
            data.onlineUsers.toLocaleString();
    }

    // 初始化所有图表
    initializeCharts() {
        this.initSalesTrendChart();
        this.initProductRankingChart();
        this.initUserLocationChart();
        this.initDeviceTypeChart();
        this.initTrafficSourceChart();
        this.initAgeDistributionChart();
        this.initRealTimeVisitsChart();
        this.initServerPerformanceChart();
        this.initInventoryAlertChart();
        this.initFinancialChart();
        this.initSatisfactionChart();
        this.initMarketShareChart();
    }

    // 1. 销售趋势分析
    initSalesTrendChart() {
        const chart = echarts.init(document.getElementById('salesTrendChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            legend: {
                data: ['销售额', '订单量'],
                textStyle: { color: '#fff' }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '销售额(万)',
                    axisLine: { lineStyle: { color: '#fff' } },
                    axisLabel: { color: '#fff' },
                    splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
                },
                {
                    type: 'value',
                    name: '订单量',
                    axisLine: { lineStyle: { color: '#fff' } },
                    axisLabel: { color: '#fff' }
                }
            ],
            series: [
                {
                    name: '销售额',
                    type: 'line',
                    data: [820, 932, 901, 934, 1290, 1330, 1320, 1450, 1200, 1100, 1300, 1500],
                    smooth: true,
                    lineStyle: { color: '#00d4ff', width: 3 },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(0,212,255,0.3)' },
                            { offset: 1, color: 'rgba(0,212,255,0.1)' }
                        ])
                    }
                },
                {
                    name: '订单量',
                    type: 'bar',
                    yAxisIndex: 1,
                    data: [120, 200, 150, 80, 70, 110, 130, 180, 160, 140, 170, 200],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#ff00ff' },
                            { offset: 1, color: 'rgba(255,0,255,0.3)' }
                        ])
                    }
                }
            ]
        };
        chart.setOption(option);
        this.charts.salesTrend = chart;
    }

    // 2. 产品销量排行
    initProductRankingChart() {
        const chart = echarts.init(document.getElementById('productRankingChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' },
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
            },
            yAxis: {
                type: 'category',
                data: ['产品E', '产品D', '产品C', '产品B', '产品A'],
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' }
            },
            series: [{
                type: 'bar',
                data: [220, 302, 341, 374, 390],
                itemStyle: {
                    color: (params) => {
                        const colors = ['#ff4757', '#ff6b7a', '#00d4ff', '#00ff88', '#ffa502'];
                        return colors[params.dataIndex];
                    }
                },
                label: {
                    show: true,
                    position: 'right',
                    color: '#fff'
                }
            }]
        };
        chart.setOption(option);
        this.charts.productRanking = chart;
    }

    // 3. 用户地域分布
    initUserLocationChart() {
        const chart = echarts.init(document.getElementById('userLocationChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            series: [{
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '50%'],
                data: [
                    { value: 335, name: '北京', itemStyle: { color: '#00d4ff' } },
                    { value: 310, name: '上海', itemStyle: { color: '#ff00ff' } },
                    { value: 234, name: '广州', itemStyle: { color: '#00ff88' } },
                    { value: 135, name: '深圳', itemStyle: { color: '#ffa502' } },
                    { value: 148, name: '其他', itemStyle: { color: '#ff4757' } }
                ],
                label: {
                    color: '#fff',
                    fontSize: 12
                },
                labelLine: {
                    lineStyle: { color: '#fff' }
                }
            }]
        };
        chart.setOption(option);
        this.charts.userLocation = chart;
    }

    // 4. 设备类型占比
    initDeviceTypeChart() {
        const chart = echarts.init(document.getElementById('deviceTypeChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            series: [{
                type: 'pie',
                radius: '70%',
                center: ['50%', '50%'],
                data: [
                    { value: 60, name: '移动端', itemStyle: { color: '#00d4ff' } },
                    { value: 30, name: 'PC端', itemStyle: { color: '#ff00ff' } },
                    { value: 10, name: '平板', itemStyle: { color: '#00ff88' } }
                ],
                label: {
                    color: '#fff',
                    fontSize: 12
                }
            }]
        };
        chart.setOption(option);
        this.charts.deviceType = chart;
    }

    // 5. 流量来源
    initTrafficSourceChart() {
        const chart = echarts.init(document.getElementById('trafficSourceChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            series: [{
                type: 'pie',
                radius: ['30%', '70%'],
                roseType: 'area',
                data: [
                    { value: 40, name: '搜索引擎', itemStyle: { color: '#00d4ff' } },
                    { value: 25, name: '直接访问', itemStyle: { color: '#ff00ff' } },
                    { value: 20, name: '社交媒体', itemStyle: { color: '#00ff88' } },
                    { value: 10, name: '邮件营销', itemStyle: { color: '#ffa502' } },
                    { value: 5, name: '其他', itemStyle: { color: '#ff4757' } }
                ],
                label: {
                    color: '#fff',
                    fontSize: 10
                }
            }]
        };
        chart.setOption(option);
        this.charts.trafficSource = chart;
    }

    // 6. 年龄分布
    initAgeDistributionChart() {
        const chart = echarts.init(document.getElementById('ageDistributionChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['18-25', '26-35', '36-45', '46-55', '55+'],
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' }
            },
            yAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
            },
            series: [{
                type: 'bar',
                data: [25, 35, 20, 15, 5],
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00ff88' },
                        { offset: 1, color: 'rgba(0,255,136,0.3)' }
                    ])
                }
            }]
        };
        chart.setOption(option);
        this.charts.ageDistribution = chart;
    }

    // 7. 实时访问量
    initRealTimeVisitsChart() {
        const chart = echarts.init(document.getElementById('realTimeVisitsChart'));
        const data = [];
        const time = [];
        for (let i = 0; i < 20; i++) {
            const now = new Date();
            now.setMinutes(now.getMinutes() - (19 - i));
            time.push(now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
            data.push(Math.floor(Math.random() * 100) + 50);
        }

        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'category',
                data: time,
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
            },
            series: [{
                type: 'line',
                data: data,
                smooth: true,
                lineStyle: { color: '#ff4757', width: 2 },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(255,71,87,0.3)' },
                        { offset: 1, color: 'rgba(255,71,87,0.1)' }
                    ])
                },
                symbol: 'circle',
                symbolSize: 4
            }]
        };
        chart.setOption(option);
        this.charts.realTimeVisits = chart;
    }

    // 8. 服务器性能
    initServerPerformanceChart() {
        const chart = echarts.init(document.getElementById('serverPerformanceChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            legend: {
                data: ['CPU使用率', '内存使用率', '磁盘使用率'],
                textStyle: { color: '#fff', fontSize: 10 }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
                type: 'value',
                max: 100,
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff', formatter: '{value}%' },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
            },
            series: [
                {
                    name: 'CPU使用率',
                    type: 'line',
                    data: [65, 70, 75, 80, 85, 78],
                    lineStyle: { color: '#00d4ff' },
                    itemStyle: { color: '#00d4ff' }
                },
                {
                    name: '内存使用率',
                    type: 'line',
                    data: [45, 50, 55, 60, 65, 58],
                    lineStyle: { color: '#ff00ff' },
                    itemStyle: { color: '#ff00ff' }
                },
                {
                    name: '磁盘使用率',
                    type: 'line',
                    data: [25, 28, 30, 32, 35, 33],
                    lineStyle: { color: '#00ff88' },
                    itemStyle: { color: '#00ff88' }
                }
            ]
        };
        chart.setOption(option);
        this.charts.serverPerformance = chart;
    }

    // 9. 库存预警
    initInventoryAlertChart() {
        const chart = echarts.init(document.getElementById('inventoryAlertChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' },
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['产品A', '产品B', '产品C', '产品D', '产品E'],
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
            },
            series: [{
                type: 'bar',
                data: [
                    { value: 15, itemStyle: { color: '#ff4757' } }, // 低库存
                    { value: 45, itemStyle: { color: '#ffa502' } }, // 中等库存
                    { value: 80, itemStyle: { color: '#00ff88' } }, // 充足库存
                    { value: 25, itemStyle: { color: '#ff4757' } }, // 低库存
                    { value: 60, itemStyle: { color: '#00ff88' } }  // 充足库存
                ],
                markLine: {
                    data: [{ yAxis: 30, lineStyle: { color: '#ffa502', type: 'dashed' } }],
                    label: { formatter: '安全库存线', color: '#ffa502' }
                }
            }]
        };
        chart.setOption(option);
        this.charts.inventoryAlert = chart;
    }

    // 10. 财务收支分析
    initFinancialChart() {
        const chart = echarts.init(document.getElementById('financialChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            legend: {
                data: ['收入', '支出', '利润'],
                textStyle: { color: '#fff' }
            },
            grid: {
                left: '3%', right: '4%', bottom: '3%', containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月'],
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff' }
            },
            yAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#fff' } },
                axisLabel: { color: '#fff', formatter: '{value}万' },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
            },
            series: [
                {
                    name: '收入',
                    type: 'bar',
                    data: [500, 600, 700, 800, 750, 900],
                    itemStyle: { color: '#00ff88' }
                },
                {
                    name: '支出',
                    type: 'bar',
                    data: [300, 350, 400, 450, 420, 500],
                    itemStyle: { color: '#ff4757' }
                },
                {
                    name: '利润',
                    type: 'line',
                    data: [200, 250, 300, 350, 330, 400],
                    lineStyle: { color: '#00d4ff', width: 3 },
                    itemStyle: { color: '#00d4ff' }
                }
            ]
        };
        chart.setOption(option);
        this.charts.financial = chart;
    }

    // 11. 客户满意度
    initSatisfactionChart() {
        const chart = echarts.init(document.getElementById('satisfactionChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                formatter: '{a} <br/>{b} : {c}%'
            },
            series: [{
                name: '客户满意度',
                type: 'gauge',
                radius: '90%',
                detail: {
                    formatter: '{value}%',
                    color: '#fff',
                    fontSize: 20
                },
                data: [{ value: 87, name: '满意度' }],
                axisLine: {
                    lineStyle: {
                        width: 10,
                        color: [
                            [0.3, '#ff4757'],
                            [0.7, '#ffa502'],
                            [1, '#00ff88']
                        ]
                    }
                },
                pointer: {
                    itemStyle: { color: '#00d4ff' }
                },
                axisTick: {
                    distance: -10,
                    length: 8,
                    lineStyle: { color: '#fff', width: 2 }
                },
                splitLine: {
                    distance: -10,
                    length: 15,
                    lineStyle: { color: '#fff', width: 3 }
                },
                axisLabel: {
                    color: '#fff',
                    distance: 20,
                    fontSize: 12
                }
            }]
        };
        chart.setOption(option);
        this.charts.satisfaction = chart;
    }

    // 12. 市场份额
    initMarketShareChart() {
        const chart = echarts.init(document.getElementById('marketShareChart'));
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#00d4ff',
                textStyle: { color: '#fff' }
            },
            series: [{
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '50%'],
                data: [
                    { value: 35, name: '我们公司', itemStyle: { color: '#00d4ff' } },
                    { value: 25, name: '竞争对手A', itemStyle: { color: '#ff00ff' } },
                    { value: 20, name: '竞争对手B', itemStyle: { color: '#00ff88' } },
                    { value: 15, name: '竞争对手C', itemStyle: { color: '#ffa502' } },
                    { value: 5, name: '其他', itemStyle: { color: '#ff4757' } }
                ],
                label: {
                    color: '#fff',
                    fontSize: 10
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        chart.setOption(option);
        this.charts.marketShare = chart;
    }

    // 启动自动更新
    startAutoUpdate() {
        this.updateInterval = setInterval(() => {
            this.updateMetrics();
            this.updateRealTimeCharts();
        }, 5000); // 每5秒更新一次
    }

    // 更新实时图表数据
    updateRealTimeCharts() {
        // 更新实时访问量图表
        if (this.charts.realTimeVisits) {
            const option = this.charts.realTimeVisits.getOption();
            const newData = Math.floor(Math.random() * 100) + 50;
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

            option.series[0].data.shift();
            option.series[0].data.push(newData);
            option.xAxis[0].data.shift();
            option.xAxis[0].data.push(timeStr);

            this.charts.realTimeVisits.setOption(option);
        }

        // 更新客户满意度
        if (this.charts.satisfaction) {
            const newValue = Math.floor(Math.random() * 20) + 80;
            const option = this.charts.satisfaction.getOption();
            option.series[0].data[0].value = newValue;
            this.charts.satisfaction.setOption(option);
        }
    }

    // 窗口大小改变时重新调整图表
    handleResize() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    // 销毁仪表板
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.dispose) {
                chart.dispose();
            }
        });
    }
}

// 初始化仪表板
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new DashboardController();

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        dashboard.handleResize();
    });
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (dashboard) {
        dashboard.destroy();
    }
});
